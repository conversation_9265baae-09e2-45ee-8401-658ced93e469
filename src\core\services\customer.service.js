/**
 * Customer Service
 * Handles API calls related to customers
 */

import { handleApiResponse } from '../utils/api.utils';
import { apiGet, apiPost, apiPut } from '../utils/api.interceptor';

/**
 * Service for handling customer-related API operations
 */
class CustomerService {
  /**
   * Get all customers with pagination and filtering
   * @param {Object} pagination - Pagination parameters
   * @param {Object} commonFilter - Common filter parameters
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with customers data
   */
  async getCustomers(pagination = {}, commonFilter = {}, options = {}) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add pagination parameters
      if (pagination.page) queryParams.append('page', pagination.page);
      if (pagination.pageSize) queryParams.append('pageSize', pagination.pageSize);
      if (pagination.sortBy) queryParams.append('sortBy', pagination.sortBy);
      if (pagination.sortOrder) queryParams.append('sortOrder', pagination.sortOrder);
      
      // Add common filter parameters
      if (commonFilter.search) queryParams.append('search', commonFilter.search);
      if (commonFilter.status) queryParams.append('status', commonFilter.status);
      if (commonFilter.country) queryParams.append('country', commonFilter.country);
      
      const queryString = queryParams.toString();
      const url = queryString ? `/customer?${queryString}` : '/customer';
      
      const response = await apiGet(url, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  /**
   * Get customer by ID
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with customer data
   */
  async getCustomerById(customerId, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const response = await apiGet(`/customer/Get/${customerId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new customer
   * @param {Object} customerData - Customer data object
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with creation response
   */
  async createCustomer(customerData, options = {}) {
    try {
      // Validate required fields
      if (!customerData.name) {
        throw new Error('Customer name is required');
      }

      // Transform frontend data to backend format
      const customerRequest = this.transformToBackendFormat(customerData);

      const response = await apiPost('/customer/Create', customerRequest, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  /**
   * Update an existing customer
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} customerData - Updated customer data
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with update response
   */
  async updateCustomer(customerId, customerData, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      // Transform frontend data to backend format
      const updateRequest = this.transformToBackendFormat(customerData, true);

      const response = await apiPut(`/customer/Update/${customerId}`, updateRequest, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  /**
   * Delete a customer
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with deletion response
   */
  async deleteCustomer(customerId, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const response = await apiPost('/customer/Delete', customerId, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  }

  /**
   * Get terms list with pagination
   * @param {Object} pagination - Pagination parameters
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with terms data
   */
  async getTerms(pagination = {}, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (pagination.page) queryParams.append('page', pagination.page);
      if (pagination.pageSize) queryParams.append('pageSize', pagination.pageSize);
      
      const queryString = queryParams.toString();
      const url = queryString ? `/customer/Terms?${queryString}` : '/customer/Terms';
      
      const response = await apiGet(url, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching terms:', error);
      throw error;
    }
  }

  /**
   * Get term by ID
   * @param {string} termId - Term ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with term data
   */
  async getTermById(termId, options = {}) {
    try {
      if (!termId) {
        throw new Error('Term ID is required');
      }

      const response = await apiGet(`/customer/Term/${termId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching term by ID:', error);
      throw error;
    }
  }

  /**
   * Get address by ID
   * @param {string} addressId - Address ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with address data
   */
  async getAddressById(addressId, options = {}) {
    try {
      if (!addressId) {
        throw new Error('Address ID is required');
      }

      const response = await apiGet(`/customer/Address/${addressId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching address by ID:', error);
      throw error;
    }
  }

  /**
   * Transform frontend customer data to backend format
   * @param {Object} customerData - Frontend customer data
   * @param {boolean} isUpdate - Whether this is for update operation
   * @returns {Object} - Backend formatted customer data
   */
  transformToBackendFormat(customerData, isUpdate = false) {
    const baseData = {
      name: customerData.name || '',
      email: customerData.email || '',
      // Add other customer fields as needed
    };

    // Add address if provided
    if (customerData.phone || customerData.address || customerData.city || customerData.country) {
      baseData.address = {
        phoneNo: customerData.phone || '',
        address1: customerData.address || '',
        city: customerData.city || '',
        country: customerData.country || '',
        // Add other address fields as needed
      };
    }

    // For update operations, include ID if available
    if (isUpdate && customerData.id) {
      baseData.id = customerData.id;
    }

    return baseData;
  }

  /**
   * Transform backend customer data to frontend format
   * @param {Object} backendData - Backend customer data
   * @returns {Object} - Frontend formatted customer data
   */
  transformToFrontendFormat(backendData) {
    if (!backendData) return null;

    return {
      id: backendData.id,
      name: backendData.name || '',
      email: backendData.email || '',
      phone: backendData.address?.phoneNo || '',
      address: backendData.address?.address1 || '',
      city: backendData.address?.city || '',
      country: backendData.address?.country || '',
      status: backendData.status || 'Active',
      createdAt: backendData.createdAt || new Date().toISOString(),
      updatedAt: backendData.updatedAt || new Date().toISOString(),
      // Add other fields as needed
    };
  }

  /**
   * Search customers by name, phone, or email
   * @param {string} searchTerm - Search term
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with search results
   */
  async searchCustomers(searchTerm, options = {}) {
    try {
      const pagination = { page: 1, pageSize: 50 };
      const commonFilter = { search: searchTerm };
      
      return await this.getCustomers(pagination, commonFilter, options);
    } catch (error) {
      console.error('Error searching customers:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const customerService = new CustomerService();
export default customerService;
